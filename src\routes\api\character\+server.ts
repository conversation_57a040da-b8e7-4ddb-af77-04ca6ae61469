import { db } from "$/lib/server/db";
import { characterTable } from "$/lib/server/db/schema";
import {type RequestHandler, error } from "@sveltejs/kit";

export const PUT: RequestHandler = async ({ request }) => {
    const {
        name,
        targetLength,
        offsetPos,
        offsetScale,
        referenceCurve,
    } = await request.json();

    if (typeof name !== "string") return error(400);
    if (!Array.isArray(referenceCurve) || !referenceCurve.every((x: any) => typeof x === "number")) return error(400);

    const rows = await db.insert(characterTable)
        .values({
            name,
            referenceCurve,
            targetLength: 1,
            offsetPos: [0, 0, 0],
            offsetScale: [1, 1, 1],
        })
        .returning({
            id,
        });

    const id = crypto.randomUUID();

    return new Response(JSON.stringify({
        id,
    }));
};