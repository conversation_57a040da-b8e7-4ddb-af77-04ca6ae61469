{"name": "height-chart", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@types/three": "^0.178.1", "drizzle-kit": "^0.31.4", "svelte": "^5.37.3", "svelte-check": "^4.3.1", "typescript": "^5.9.2", "vite": "^7.0.6"}, "dependencies": {"@sveltejs/adapter-auto": "^6.0.1", "@threlte/core": "^8.1.4", "@threlte/extras": "^9.4.3", "@vaie/hui": "^0.0.0", "drizzle-orm": "^0.44.4", "postgres": "^3.4.5", "sass": "^1.89.2", "three": "^0.179.1"}}